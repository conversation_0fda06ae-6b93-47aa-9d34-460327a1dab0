"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { trpc } from '@/lib/trpc-client';
import { toast } from 'react-toastify';
import { CreateDiscountSchema, CreateDiscountInput } from '@/server/features/discount/discount.validation';

type CreateDiscountForm = CreateDiscountInput;

export default function CreateDiscountPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateDiscountForm>({
    resolver: zodResolver(CreateDiscountSchema),
    defaultValues: {
      isActive: true,
      validFrom: new Date(),
      validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    },
  });

  const watchedValidFrom = watch('validFrom');
  const watchedValidTo = watch('validTo');
  const watchedIsActive = watch('isActive');

  // Create mutation
  const createMutation = trpc.discount.create.useMutation({
    onSuccess: () => {
      toast.success('Discount created successfully');
      router.push('/admin/discounts');
    },
    onError: (error) => {
      toast.error(`Failed to create discount: ${error.message}`);
      setIsSubmitting(false);
    },
  });

  const onSubmit = async (data: CreateDiscountForm) => {
    setIsSubmitting(true);
    try {
      await createMutation.mutateAsync({
        ...data,
        type: 'PERCENTAGE_CAP' as const,
      });
    } catch (_error) {
      // Error is handled by the mutation's onError callback
    }
  };

  const handleCancel = () => {
    router.push('/admin/discounts');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Discounts
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Create Discount</h1>
          <p className="text-muted-foreground">
            Create a new discount rule for your customers
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Set up the basic details for your discount
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Discount Name *</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="e.g., Summer Sale 2024"
                    className={cn(errors.name && 'border-red-500')}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...register('description')}
                    placeholder="Optional description for internal use"
                    rows={3}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-500">{errors.description.message}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={watchedIsActive}
                    onCheckedChange={(checked) => setValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active</Label>
                  <span className="text-sm text-muted-foreground">
                    (Discount will be available for use)
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Discount Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Discount Configuration</CardTitle>
                <CardDescription>
                  Configure the discount percentage, cap, and minimum cart value
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="percentage">Discount Percentage (%) *</Label>
                    <Input
                      id="percentage"
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="100"
                      {...register('percentage', { valueAsNumber: true })}
                      placeholder="10"
                      className={cn(errors.percentage && 'border-red-500')}
                    />
                    {errors.percentage && (
                      <p className="text-sm text-red-500">{errors.percentage.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxDiscountAmount">Maximum Discount Amount (₹) *</Label>
                    <Input
                      id="maxDiscountAmount"
                      type="number"
                      step="0.01"
                      min="0"
                      {...register('maxDiscountAmount', { valueAsNumber: true })}
                      placeholder="50"
                      className={cn(errors.maxDiscountAmount && 'border-red-500')}
                    />
                    {errors.maxDiscountAmount && (
                      <p className="text-sm text-red-500">{errors.maxDiscountAmount.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minCartValue">Minimum Cart Value (₹) *</Label>
                  <Input
                    id="minCartValue"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('minCartValue', { valueAsNumber: true })}
                    placeholder="200"
                    className={cn(errors.minCartValue && 'border-red-500')}
                  />
                  {errors.minCartValue && (
                    <p className="text-sm text-red-500">{errors.minCartValue.message}</p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    Customers must have at least this amount in their cart to qualify
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxUsage">Maximum Usage Limit</Label>
                  <Input
                    id="maxUsage"
                    type="number"
                    min="1"
                    {...register('maxUsage', { valueAsNumber: true })}
                    placeholder="1000"
                  />
                  {errors.maxUsage && (
                    <p className="text-sm text-red-500">{errors.maxUsage.message}</p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    Leave empty for unlimited usage
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Validity Period */}
            <Card>
              <CardHeader>
                <CardTitle>Validity Period</CardTitle>
                <CardDescription>
                  Set when this discount should be active
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Date *</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !watchedValidFrom && 'text-muted-foreground',
                            errors.validFrom && 'border-red-500'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {watchedValidFrom ? (
                            format(watchedValidFrom, 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={watchedValidFrom}
                          onSelect={(date) => setValue('validFrom', date || new Date())}
                        />
                      </PopoverContent>
                    </Popover>
                    {errors.validFrom && (
                      <p className="text-sm text-red-500">{errors.validFrom.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>End Date *</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !watchedValidTo && 'text-muted-foreground',
                            errors.validTo && 'border-red-500'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {watchedValidTo ? (
                            format(watchedValidTo, 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={watchedValidTo}
                          onSelect={(date) => setValue('validTo', date || new Date())}
                        />
                      </PopoverContent>
                    </Popover>
                    {errors.validTo && (
                      <p className="text-sm text-red-500">{errors.validTo.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Discount Preview</CardTitle>
                <CardDescription>
                  Preview how your discount will work
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-muted rounded-lg space-y-2">
                  <h4 className="font-medium">Discount Rule</h4>
                  <p className="text-sm text-muted-foreground">
                    {watch('percentage') || 0}% off up to {formatCurrency(watch('maxDiscountAmount') || 0)} on cart value above {formatCurrency(watch('minCartValue') || 0)}
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Example Calculations</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Cart: ₹500</span>
                      <span className="text-emerald-600">
                        Save: {formatCurrency(Math.min((500 * (watch('percentage') || 0)) / 100, watch('maxDiscountAmount') || 0))}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cart: ₹1000</span>
                      <span className="text-emerald-600">
                        Save: {formatCurrency(Math.min((1000 * (watch('percentage') || 0)) / 100, watch('maxDiscountAmount') || 0))}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cart: ₹2000</span>
                      <span className="text-emerald-600">
                        Save: {formatCurrency(Math.min((2000 * (watch('percentage') || 0)) / 100, watch('maxDiscountAmount') || 0))}
                      </span>
                    </div>
                  </div>
                </div>

                {watch('validFrom') && watch('validTo') && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Active Period</h4>
                    <p className="text-sm text-muted-foreground">
                      {format(watch('validFrom'), 'MMM dd, yyyy')} - {format(watch('validTo'), 'MMM dd, yyyy')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Creating...' : 'Create Discount'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}