"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import { trpc } from '@/lib/trpc-client';
import { toast } from 'react-toastify';

interface Discount {
  id: string;
  name: string;
  description?: string;
  type: 'PERCENTAGE_CAP';
  isActive: boolean;
  validFrom: Date;
  validTo: Date;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
  maxUsage?: number;
  percentage?: number;
  maxDiscountAmount?: number;
  minCartValue?: number;
}

export default function DiscountsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  // tRPC queries
  const {
    data: discountsData,
    isLoading,
    refetch
  } = trpc.discount.list.useQuery({
    page: currentPage,
    limit: pageSize,
    filters: {
      isActive: statusFilter === 'all' ? undefined : statusFilter === 'active',
      search: searchTerm || undefined,
    }
  });

  // Delete mutation
  const deleteMutation = trpc.discount.delete.useMutation({
    onSuccess: () => {
      toast.success('Discount deleted successfully');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete discount: ${error.message}`);
    }
  });

  const handleCreateDiscount = () => {
    router.push('/admin/discounts/create');
  };

  const handleEditDiscount = (discount: Discount) => {
    router.push(`/admin/discounts/${discount.id}/edit`);
  };

  const handleDeleteDiscount = async (discount: Discount) => {
    if (window.confirm(`Are you sure you want to delete "${discount.name}"?`)) {
      try {
        await deleteMutation.mutateAsync({ id: discount.id });
      } catch (error) {
        // Error is handled by the mutation's onError callback
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  const isDiscountExpired = (validTo: Date) => {
    return new Date(validTo) < new Date();
  };

  const getDiscountStatus = (discount: Discount) => {
    if (!discount.isActive) return 'inactive';
    if (isDiscountExpired(discount.validTo)) return 'inactive';
    return 'active';
  };

  const columns: Column<Discount>[] = [
    {
      key: 'name',
      header: 'Discount Name',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col">
          <span className="font-medium text-foreground">{discount.name}</span>
          {discount.description && (
            <span className="text-sm text-muted-foreground">{discount.description}</span>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      header: 'Type',
      render: (discount) => (
        <div className="flex flex-col">
          <Badge variant="outline" className="w-fit">
            {discount.percentage}% Off
          </Badge>
          <span className="text-xs text-muted-foreground mt-1">
            Cap: {formatCurrency(discount.maxDiscountAmount || 0)}
          </span>
        </div>
      ),
    },
    {
      key: 'minCartValue',
      header: 'Min Cart Value',
      sortable: true,
      render: (discount) => formatCurrency(discount.minCartValue || 0),
      getSortValue: (discount) => discount.minCartValue || 0,
    },
    {
      key: 'validFrom',
      header: 'Valid Period',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col text-sm">
          <span>From: {formatDate(discount.validFrom)}</span>
          <span>To: {formatDate(discount.validTo)}</span>
        </div>
      ),
      getSortValue: (discount) => new Date(discount.validFrom).getTime(),
    },
    {
      key: 'usageCount',
      header: 'Usage',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col text-sm">
          <span className="font-medium">{discount.usageCount}</span>
          {discount.maxUsage && (
            <span className="text-muted-foreground">
              / {discount.maxUsage} max
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (discount) => (
        <StatusBadge status={getDiscountStatus(discount)} />
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (discount) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditDiscount(discount)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteDiscount(discount)}
            disabled={deleteMutation.isLoading}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const filteredDiscounts = discountsData?.discounts || [];
  const totalCount = discountsData?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Discounts</h1>
          <p className="text-muted-foreground">
            Manage discount rules and promotional offers
          </p>
        </div>
        <Button onClick={handleCreateDiscount} className="w-fit">
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Discount
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Discounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Discounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">
              {filteredDiscounts.filter(d => getDiscountStatus(d) === 'active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredDiscounts.reduce((sum, d) => sum + d.usageCount, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Expired
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {filteredDiscounts.filter(d => isDiscountExpired(d.validTo)).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
          <CardDescription>
            Filter and search through your discount rules
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search discounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <DataTable
        data={filteredDiscounts}
        columns={columns}
        loading={isLoading}
        emptyMessage="No discounts found. Create your first discount to get started."
        onRowClick={handleEditDiscount}
        getItemId={(discount) => discount.id}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} discounts
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}


    </div>
  );
}