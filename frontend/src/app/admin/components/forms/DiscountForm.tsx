"use client";

import React, { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { 
  CreateDiscountSchema, 
  UpdateDiscountSchema, 
  CreateDiscountInput,
  UpdateDiscountInput,
  DiscountResponse 
} from '@/server/features/discount/discount.validation';

type DiscountFormMode = 'create' | 'edit';

interface DiscountFormProps {
  mode: DiscountFormMode;
  initialData?: DiscountResponse;
  onSubmit: (data: CreateDiscountInput | UpdateDiscountInput) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

type FormData = CreateDiscountInput | UpdateDiscountInput;

export default function DiscountForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}: DiscountFormProps) {
  // Determine schema and default values based on mode
  const schema = mode === 'create' ? CreateDiscountSchema : UpdateDiscountSchema;
  
  const defaultValues = useMemo(() => {
    if (mode === 'edit' && initialData) {
      return {
        id: initialData.id,
        name: initialData.name,
        description: initialData.description || '',
        type: initialData.type,
        isActive: initialData.isActive,
        validFrom: initialData.validFrom,
        validTo: initialData.validTo,
        percentage: initialData.percentage || 1,
        maxDiscountAmount: initialData.maxDiscountAmount || 100,
        minCartValue: initialData.minCartValue || 0,
        maxUsage: initialData.maxUsage,
      } as UpdateDiscountInput;
    }
    
    return {
      name: '',
      description: '',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date(),
      validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      percentage: 1,
      maxDiscountAmount: 100,
      minCartValue: 0,
      maxUsage: undefined,
    } as CreateDiscountInput;
  }, [mode, initialData]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const watchedValidFrom = watch('validFrom');
  const watchedValidTo = watch('validTo');
  const watchedIsActive = watch('isActive');
  const watchedPercentage = watch('percentage');
  const watchedMaxDiscountAmount = watch('maxDiscountAmount');
  const watchedMinCartValue = watch('minCartValue');

  // Memoized utility functions
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);

  const formatDate = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  }, []);

  // Memoized preview calculations
  const previewData = useMemo(() => {
    const percentage = watchedPercentage || 0;
    const maxDiscount = watchedMaxDiscountAmount || 0;
    const minCart = watchedMinCartValue || 0;

    const sampleCartValues = [200, 500, 1000];

    return sampleCartValues.map(cartValue => {
      const meetsMinimum = cartValue >= minCart;

      if (!meetsMinimum) {
        return {
          cartValue,
          discountAmount: 0,
          finalAmount: cartValue,
          applies: false,
          reason: `Minimum cart value: ${formatCurrency(minCart)}`
        };
      }

      const rawDiscount = cartValue * (percentage / 100);
      const actualDiscount = Math.min(rawDiscount, maxDiscount);
      const finalAmount = cartValue - actualDiscount;

      return {
        cartValue,
        discountAmount: actualDiscount,
        finalAmount,
        applies: true,
        reason: actualDiscount === maxDiscount ? 'Capped at maximum' : 'Percentage applied'
      };
    });
  }, [watchedPercentage, watchedMaxDiscountAmount, watchedMinCartValue, formatCurrency]);

  const handleFormSubmit = useCallback(async (data: FormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  }, [onSubmit]);

  const handleCancel = useCallback(() => {
    onCancel();
  }, [onCancel]);

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Hidden field for type in create mode */}
      {mode === 'create' && (
        <input type="hidden" {...register('type')} value="PERCENTAGE_CAP" />
      )}
      
      {/* Hidden field for id in edit mode */}
      {mode === 'edit' && initialData && (
        <input type="hidden" {...register('id')} value={initialData.id} />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Configure the basic details of your discount
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Discount Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="e.g., Summer Sale 20% Off"
                  className={cn(errors.name && 'border-red-500')}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{String(errors.name.message)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Optional description for internal use"
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{String(errors.description.message)}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={watchedIsActive}
                  onCheckedChange={(checked) => setValue('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
                <span className="text-sm text-muted-foreground">
                  (Discount will be available for use)
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Discount Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Discount Configuration</CardTitle>
              <CardDescription>
                Configure the discount percentage, cap, and minimum cart value
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="percentage">Discount Percentage (%) *</Label>
                  <Input
                    id="percentage"
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="100"
                    {...register('percentage', { valueAsNumber: true })}
                    placeholder="10"
                    className={cn(errors.percentage && 'border-red-500')}
                  />
                  {errors.percentage && (
                    <p className="text-sm text-red-500">{String(errors.percentage.message)}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxDiscountAmount">Maximum Discount Amount (₹) *</Label>
                  <Input
                    id="maxDiscountAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('maxDiscountAmount', { valueAsNumber: true })}
                    placeholder="50"
                    className={cn(errors.maxDiscountAmount && 'border-red-500')}
                  />
                  {errors.maxDiscountAmount && (
                    <p className="text-sm text-red-500">{String(errors.maxDiscountAmount.message)}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="minCartValue">Minimum Cart Value (₹) *</Label>
                <Input
                  id="minCartValue"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('minCartValue', { valueAsNumber: true })}
                  placeholder="200"
                  className={cn(errors.minCartValue && 'border-red-500')}
                />
                {errors.minCartValue && (
                  <p className="text-sm text-red-500">{String(errors.minCartValue.message)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxUsage">Maximum Usage (Optional)</Label>
                <Input
                  id="maxUsage"
                  type="number"
                  min="1"
                  {...register('maxUsage', { 
                    valueAsNumber: true,
                    setValueAs: (value) => value === '' ? undefined : Number(value)
                  })}
                  placeholder="1000"
                  className={cn(errors.maxUsage && 'border-red-500')}
                />
                {errors.maxUsage && (
                  <p className="text-sm text-red-500">{String(errors.maxUsage.message)}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Leave empty for unlimited usage
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Validity Period */}
          <Card>
            <CardHeader>
              <CardTitle>Validity Period</CardTitle>
              <CardDescription>
                Set when this discount is valid
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Valid From *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !watchedValidFrom && "text-muted-foreground",
                        errors.validFrom && "border-red-500"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {watchedValidFrom ? formatDate(watchedValidFrom) : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={watchedValidFrom}
                      onSelect={(date) => setValue('validFrom', date || new Date())}
                    />
                  </PopoverContent>
                </Popover>
                {errors.validFrom && (
                  <p className="text-sm text-red-500">{String(errors.validFrom.message)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Valid To *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !watchedValidTo && "text-muted-foreground",
                        errors.validTo && "border-red-500"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {watchedValidTo ? formatDate(watchedValidTo) : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={watchedValidTo}
                      onSelect={(date) => setValue('validTo', date || new Date())}
                    />
                  </PopoverContent>
                </Popover>
                {errors.validTo && (
                  <p className="text-sm text-red-500">{String(errors.validTo.message)}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Discount Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Discount Preview</CardTitle>
              <CardDescription>
                See how your discount applies to different cart values
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {previewData.map((preview, index) => (
                <div key={index} className="border rounded-lg p-3 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Cart Value:</span>
                    <span className="text-lg font-semibold">
                      {formatCurrency(preview.cartValue)}
                    </span>
                  </div>

                  {preview.applies ? (
                    <>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-600">Discount:</span>
                        <span className="text-green-600 font-medium">
                          -{formatCurrency(preview.discountAmount)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span>Final Amount:</span>
                        <span className="font-medium">
                          {formatCurrency(preview.finalAmount)}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {preview.reason}
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-red-600">Discount:</span>
                        <span className="text-red-600">Not applicable</span>
                      </div>
                      <div className="text-xs text-red-600">
                        {preview.reason}
                      </div>
                    </>
                  )}
                </div>
              ))}

              {(watchedPercentage || 0) === 0 && (
                <div className="text-center text-sm text-muted-foreground py-4">
                  Set a percentage to see discount preview
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting 
                    ? (mode === 'create' ? 'Creating...' : 'Updating...') 
                    : (mode === 'create' ? 'Create Discount' : 'Update Discount')
                  }
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </form>
  );
}
